<template>
  <div class="detailed-evaluation-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button type="info" @click="handleBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
        返回
      </el-button>
      <div class="header-content">
        <h2>详细KPI评估</h2>
        <p>{{ supplierInfo.name }} - {{ supplierInfo.category }}</p>
      </div>
      <div class="header-actions">
        <el-button type="success" @click="handleSaveEvaluation">
          <el-icon><Check /></el-icon>
          保存评估
        </el-button>
        <el-button type="primary" @click="handleSubmitEvaluation">
          <el-icon><Upload /></el-icon>
          提交评估
        </el-button>
      </div>
    </div>

    <!-- 评估信息 -->
    <el-card class="evaluation-info-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="评估周期">
            <el-select v-model="evaluationInfo.period" placeholder="请选择评估周期">
              <el-option label="月度评估" value="month" />
              <el-option label="季度评估" value="quarter" />
              <el-option label="年度评估" value="year" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="评估人">
            <el-input v-model="evaluationInfo.evaluator" placeholder="请输入评估人" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="评估日期">
            <el-date-picker
              v-model="evaluationInfo.evaluationDate"
              type="date"
              placeholder="请选择评估日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="数据周期">
            <el-date-picker
              v-model="evaluationInfo.dataPeriod"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              style="width: 100%"
              format="YYYY-MM"
              value-format="YYYY-MM"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 综合得分概览 -->
    <el-card class="score-overview-card">
      <template #header>
        <div class="card-header">
          <span>综合得分概览</span>
          <div class="header-tags">
            <el-tag :type="getScoreType(calculateOverallScore)" size="large">
              综合得分: {{ calculateOverallScore }}分
            </el-tag>
            <el-tag
              :type="getRiskLevelType(calculateRiskLevel)"
              :class="getRiskLevelClass(calculateRiskLevel)"
              size="large"
              effect="dark"
            >
              <el-icon class="risk-icon">
                <component :is="getRiskIcon(calculateRiskLevel)" />
              </el-icon>
              {{ getRiskLevelText(calculateRiskLevel) }}
            </el-tag>
          </div>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="score-item">
            <div class="score-label">质量得分</div>
            <div class="score-value">{{ getCategoryScore('质量指标') }}</div>
            <div class="score-weight">权重: {{ getCategoryWeight('质量指标') }}%</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="score-item">
            <div class="score-label">交付得分</div>
            <div class="score-value">{{ getCategoryScore('交付指标') }}</div>
            <div class="score-weight">权重: {{ getCategoryWeight('交付指标') }}%</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="score-item">
            <div class="score-label">成本得分</div>
            <div class="score-value">{{ getCategoryScore('成本指标') }}</div>
            <div class="score-weight">权重: {{ getCategoryWeight('成本指标') }}%</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="score-item">
            <div class="score-label">服务得分</div>
            <div class="score-value">{{ getCategoryScore('服务指标') }}</div>
            <div class="score-weight">权重: {{ getCategoryWeight('服务指标') }}%</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- KPI评估表格 -->
    <el-card class="kpi-evaluation-card">
      <template #header>
        <div class="card-header">
          <span>KPI指标评估</span>
          <div class="header-actions">
            <el-button type="primary" size="small" @click="handleAutoCalculate">
              <el-icon><Calculator /></el-icon>
              自动计算
            </el-button>
            <el-button type="success" size="small" @click="handleImportData">
              <el-icon><Download /></el-icon>
              导入数据
            </el-button>
          </div>
        </div>
      </template>

      <!-- KPI分类标签页 -->
      <el-tabs v-model="activeKPITab" @tab-change="handleKPITabChange">
        <!-- 质量指标标签页 -->
        <el-tab-pane label="质量指标" name="quality">
          <template #label>
            <span class="tab-label">
              <el-icon><Trophy /></el-icon>
              质量指标 ({{ getCategoryWeight('质量指标') }}%)
            </span>
          </template>

          <el-table :data="qualityKPIs" style="width: 100%" class="kpi-table">
          <el-table-column prop="name" label="指标名称" min-width="200" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="weight" label="权重" width="80">
            <template #default="scope">
              {{ scope.row.weight }}%
            </template>
          </el-table-column>
          <el-table-column prop="targetValue" label="目标值" width="100" />
          <el-table-column prop="actualValue" label="实际值" width="120">
            <template #default="scope">
              <el-input
                v-model="scope.row.actualValue"
                placeholder="请输入实际值"
                @input="handleActualValueChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="achievement" label="达成率" width="120">
            <template #default="scope">
              <div class="achievement-cell">
                <el-progress
                  :percentage="scope.row.achievement"
                  :stroke-width="6"
                  :color="getAchievementColor(scope.row.achievement)"
                />
                <span class="achievement-text">{{ scope.row.achievement }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="得分" width="100">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.score"
                :min="0"
                :max="100"
                :precision="1"
                style="width: 100%"
                @change="handleScoreChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="200">
            <template #default="scope">
              <el-input
                v-model="scope.row.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入评估备注"
              />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>

        <!-- 交付指标标签页 -->
        <el-tab-pane label="交付指标" name="delivery">
          <template #label>
            <span class="tab-label">
              <el-icon><Truck /></el-icon>
              交付指标 ({{ getCategoryWeight('交付指标') }}%)
            </span>
          </template>
          <el-table :data="deliveryKPIs" style="width: 100%" class="kpi-table">
          <el-table-column prop="name" label="指标名称" min-width="200" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="weight" label="权重" width="80">
            <template #default="scope">
              {{ scope.row.weight }}%
            </template>
          </el-table-column>
          <el-table-column prop="targetValue" label="目标值" width="100" />
          <el-table-column prop="actualValue" label="实际值" width="120">
            <template #default="scope">
              <el-input
                v-model="scope.row.actualValue"
                placeholder="请输入实际值"
                @input="handleActualValueChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="achievement" label="达成率" width="120">
            <template #default="scope">
              <div class="achievement-cell">
                <el-progress
                  :percentage="scope.row.achievement"
                  :stroke-width="6"
                  :color="getAchievementColor(scope.row.achievement)"
                />
                <span class="achievement-text">{{ scope.row.achievement }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="得分" width="100">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.score"
                :min="0"
                :max="100"
                :precision="1"
                style="width: 100%"
                @change="handleScoreChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="200">
            <template #default="scope">
              <el-input
                v-model="scope.row.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入评估备注"
              />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>

        <!-- 成本指标标签页 -->
        <el-tab-pane label="成本指标" name="cost">
          <template #label>
            <span class="tab-label">
              <el-icon><Money /></el-icon>
              成本指标 ({{ getCategoryWeight('成本指标') }}%)
            </span>
          </template>
          <el-table :data="costKPIs" style="width: 100%" class="kpi-table">
          <el-table-column prop="name" label="指标名称" min-width="200" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="weight" label="权重" width="80">
            <template #default="scope">
              {{ scope.row.weight }}%
            </template>
          </el-table-column>
          <el-table-column prop="targetValue" label="目标值" width="100" />
          <el-table-column prop="actualValue" label="实际值" width="120">
            <template #default="scope">
              <el-input
                v-model="scope.row.actualValue"
                placeholder="请输入实际值"
                @input="handleActualValueChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="achievement" label="达成率" width="120">
            <template #default="scope">
              <div class="achievement-cell">
                <el-progress
                  :percentage="scope.row.achievement"
                  :stroke-width="6"
                  :color="getAchievementColor(scope.row.achievement)"
                />
                <span class="achievement-text">{{ scope.row.achievement }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="得分" width="100">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.score"
                :min="0"
                :max="100"
                :precision="1"
                style="width: 100%"
                @change="handleScoreChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="200">
            <template #default="scope">
              <el-input
                v-model="scope.row.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入评估备注"
              />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>

        <!-- 服务指标标签页 -->
        <el-tab-pane label="服务指标" name="service">
          <template #label>
            <span class="tab-label">
              <el-icon><Tools /></el-icon>
              服务指标 ({{ getCategoryWeight('服务指标') }}%)
            </span>
          </template>
          <el-table :data="serviceKPIs" style="width: 100%" class="kpi-table">
          <el-table-column prop="name" label="指标名称" min-width="200" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="weight" label="权重" width="80">
            <template #default="scope">
              {{ scope.row.weight }}%
            </template>
          </el-table-column>
          <el-table-column prop="targetValue" label="目标值" width="100" />
          <el-table-column prop="actualValue" label="实际值" width="120">
            <template #default="scope">
              <el-input
                v-model="scope.row.actualValue"
                placeholder="请输入实际值"
                @input="handleActualValueChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="achievement" label="达成率" width="120">
            <template #default="scope">
              <div class="achievement-cell">
                <el-progress
                  :percentage="scope.row.achievement"
                  :stroke-width="6"
                  :color="getAchievementColor(scope.row.achievement)"
                />
                <span class="achievement-text">{{ scope.row.achievement }}%</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="得分" width="100">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.score"
                :min="0"
                :max="100"
                :precision="1"
                style="width: 100%"
                @change="handleScoreChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="200">
            <template #default="scope">
              <el-input
                v-model="scope.row.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入评估备注"
              />
            </template>
          </el-table-column>
        </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'DetailedEvaluation',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const supplierId = route.params.id

    // 供应商信息
    const supplierInfo = reactive({
      id: supplierId,
      name: '博世汽车部件(苏州)有限公司',
      category: '制动系统'
    })

    // 评估信息
    const evaluationInfo = reactive({
      period: 'quarter',
      evaluator: '',
      evaluationDate: '',
      dataPeriod: []
    })

    // 当前活跃的KPI标签页
    const activeKPITab = ref('quality')

    // KPI数据
    const qualityKPIs = ref([])
    const deliveryKPIs = ref([])
    const costKPIs = ref([])
    const serviceKPIs = ref([])

    // 初始化KPI数据
    const initKPIData = () => {
      qualityKPIs.value = [
        {
          id: 1,
          category: '质量指标',
          name: '来料不良率(PPM)',
          unit: 'PPM',
          weight: 25,
          targetValue: '<100',
          actualValue: '85',
          achievement: 115,
          score: 95,
          remark: '表现优秀，超出目标要求'
        },
        {
          id: 2,
          category: '质量指标',
          name: '批次合格率',
          unit: '%',
          weight: 25,
          targetValue: '>99%',
          actualValue: '99.2',
          achievement: 100,
          score: 92,
          remark: '达到目标要求'
        },
        {
          id: 3,
          category: '质量指标',
          name: '客户投诉率',
          unit: 'PPM',
          weight: 15,
          targetValue: '<50',
          actualValue: '35',
          achievement: 143,
          score: 98,
          remark: '客户满意度高'
        },
        {
          id: 4,
          category: '质量指标',
          name: '质量体系审核得分',
          unit: '分',
          weight: 10,
          targetValue: '>90',
          actualValue: '92',
          achievement: 102,
          score: 94,
          remark: '质量体系运行良好'
        }
      ]

      deliveryKPIs.value = [
        {
          id: 5,
          category: '交付指标',
          name: '交期达成率(OTD)',
          unit: '%',
          weight: 20,
          targetValue: '>95%',
          actualValue: '94',
          achievement: 99,
          score: 88,
          remark: '略低于目标，需要改进'
        },
        {
          id: 6,
          category: '交付指标',
          name: '订单完成率',
          unit: '%',
          weight: 10,
          targetValue: '>98%',
          actualValue: '98.5',
          achievement: 100,
          score: 90,
          remark: '达到目标要求'
        },
        {
          id: 7,
          category: '交付指标',
          name: '计划变更频率',
          unit: '次/月',
          weight: 8,
          targetValue: '<10',
          actualValue: '8',
          achievement: 125,
          score: 92,
          remark: '计划稳定性良好'
        },
        {
          id: 8,
          category: '交付指标',
          name: '紧急订单响应时间',
          unit: '小时',
          weight: 7,
          targetValue: '<24',
          actualValue: '18',
          achievement: 133,
          score: 95,
          remark: '响应及时'
        }
      ]

      costKPIs.value = [
        {
          id: 9,
          category: '成本指标',
          name: '年度降价达成率',
          unit: '%',
          weight: 15,
          targetValue: '>3%',
          actualValue: '3.2',
          achievement: 107,
          score: 85,
          remark: '达到降价目标'
        },
        {
          id: 10,
          category: '成本指标',
          name: '成本竞争力指数',
          unit: '指数',
          weight: 10,
          targetValue: '>1.0',
          actualValue: '1.05',
          achievement: 105,
          score: 82,
          remark: '成本控制良好'
        },
        {
          id: 11,
          category: '成本指标',
          name: '价格稳定性',
          unit: '%',
          weight: 8,
          targetValue: '<5%',
          actualValue: '3.5',
          achievement: 143,
          score: 88,
          remark: '价格波动较小'
        }
      ]

      serviceKPIs.value = [
        {
          id: 12,
          category: '服务指标',
          name: '技术支持及时性',
          unit: '小时',
          weight: 5,
          targetValue: '<24',
          actualValue: '16',
          achievement: 150,
          score: 95,
          remark: '技术支持响应迅速'
        },
        {
          id: 13,
          category: '服务指标',
          name: '售后服务满意度',
          unit: '分',
          weight: 3,
          targetValue: '>4.5',
          actualValue: '4.7',
          achievement: 104,
          score: 90,
          remark: '客户满意度高'
        },
        {
          id: 14,
          category: '服务指标',
          name: '培训配合度',
          unit: '分',
          weight: 2,
          targetValue: '>4.0',
          actualValue: '4.2',
          achievement: 105,
          score: 88,
          remark: '配合度良好'
        }
      ]
    }

    // 计算类别权重
    const getCategoryWeight = (category) => {
      let totalWeight = 0
      const allKPIs = [...qualityKPIs.value, ...deliveryKPIs.value, ...costKPIs.value, ...serviceKPIs.value]
      allKPIs.forEach(kpi => {
        if (kpi.category === category) {
          totalWeight += kpi.weight
        }
      })
      return totalWeight
    }

    // 获取达成率颜色
    const getAchievementColor = (achievement) => {
      if (achievement >= 100) return '#67C23A'
      if (achievement >= 90) return '#E6A23C'
      return '#F56C6C'
    }

    // 处理实际值变化
    const handleActualValueChange = (row) => {
      // 根据实际值和目标值计算达成率
      calculateAchievement(row)
      // 根据达成率自动计算得分
      calculateScore(row)
    }

    // 计算达成率
    const calculateAchievement = (row) => {
      const actual = parseFloat(row.actualValue)
      const target = row.targetValue

      if (isNaN(actual)) {
        row.achievement = 0
        return
      }

      // 根据目标值格式判断计算方式
      if (target.includes('>')) {
        const targetValue = parseFloat(target.replace('>', '').replace('%', ''))
        row.achievement = Math.round((actual / targetValue) * 100)
      } else if (target.includes('<')) {
        const targetValue = parseFloat(target.replace('<', '').replace('PPM', ''))
        row.achievement = Math.round((targetValue / actual) * 100)
      } else {
        // 等于目标值的情况
        const targetValue = parseFloat(target.replace('%', '').replace('分', ''))
        row.achievement = Math.round((actual / targetValue) * 100)
      }

      // 限制达成率最大值为200%
      if (row.achievement > 200) row.achievement = 200
    }

    // 根据达成率计算得分
    const calculateScore = (row) => {
      const achievement = row.achievement
      if (achievement >= 120) {
        row.score = 100
      } else if (achievement >= 100) {
        row.score = 90 + (achievement - 100) * 0.5
      } else if (achievement >= 80) {
        row.score = 70 + (achievement - 80) * 1
      } else if (achievement >= 60) {
        row.score = 50 + (achievement - 60) * 1
      } else {
        row.score = achievement * 0.8
      }
      row.score = Math.round(row.score * 10) / 10
    }

    // 处理得分变化
    const handleScoreChange = (row) => {
      // 得分变化时可以添加一些验证逻辑
      if (row.score < 0) row.score = 0
      if (row.score > 100) row.score = 100
    }

    // 自动计算所有KPI
    const handleAutoCalculate = () => {
      const allKPIs = [...qualityKPIs.value, ...deliveryKPIs.value, ...costKPIs.value, ...serviceKPIs.value]
      allKPIs.forEach(kpi => {
        if (kpi.actualValue) {
          calculateAchievement(kpi)
          calculateScore(kpi)
        }
      })
      ElMessage.success('自动计算完成')
    }

    // 导入数据
    const handleImportData = () => {
      ElMessage.info('数据导入功能开发中...')
    }

    // 处理KPI标签页切换
    const handleKPITabChange = (tabName) => {
      activeKPITab.value = tabName
      console.log('切换到标签页:', tabName)
    }

    // 返回上一页
    const handleBack = () => {
      router.back()
    }

    // 保存评估
    const handleSaveEvaluation = async () => {
      try {
        // 验证必填字段
        if (!evaluationInfo.period) {
          ElMessage.warning('请选择评估周期')
          return
        }
        if (!evaluationInfo.evaluator) {
          ElMessage.warning('请输入评估人')
          return
        }
        if (!evaluationInfo.evaluationDate) {
          ElMessage.warning('请选择评估日期')
          return
        }

        // 收集所有KPI数据
        const evaluationData = {
          supplierInfo,
          evaluationInfo,
          kpiData: {
            quality: qualityKPIs.value,
            delivery: deliveryKPIs.value,
            cost: costKPIs.value,
            service: serviceKPIs.value
          }
        }

        console.log('保存评估数据:', evaluationData)
        ElMessage.success('评估数据已保存')
      } catch (error) {
        ElMessage.error('保存失败: ' + error.message)
      }
    }

    // 提交评估
    const handleSubmitEvaluation = async () => {
      try {
        await ElMessageBox.confirm(
          '提交后将无法修改，确认提交评估结果？',
          '确认提交',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        // 先保存数据
        await handleSaveEvaluation()

        // 提交评估
        console.log('提交评估')
        ElMessage.success('评估已提交')

        // 返回列表页
        router.push('/supplier-performance')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('提交失败: ' + error.message)
        }
      }
    }

    // 计算类别得分
    const getCategoryScore = (category) => {
      let totalWeightedScore = 0
      let totalWeight = 0
      const allKPIs = [...qualityKPIs.value, ...deliveryKPIs.value, ...costKPIs.value, ...serviceKPIs.value]

      allKPIs.forEach(kpi => {
        if (kpi.category === category && kpi.score && kpi.weight) {
          totalWeightedScore += kpi.score * kpi.weight
          totalWeight += kpi.weight
        }
      })

      return totalWeight > 0 ? Math.round((totalWeightedScore / totalWeight) * 10) / 10 : 0
    }

    // 获取得分类型
    const getScoreType = (score) => {
      if (score >= 90) return 'success'
      if (score >= 80) return 'primary'
      if (score >= 70) return 'warning'
      return 'danger'
    }

    // 计算综合得分
    const calculateOverallScore = computed(() => {
      const allKPIs = [...qualityKPIs.value, ...deliveryKPIs.value, ...costKPIs.value, ...serviceKPIs.value]
      let totalWeightedScore = 0
      let totalWeight = 0

      allKPIs.forEach(kpi => {
        if (kpi.score && kpi.weight) {
          totalWeightedScore += kpi.score * kpi.weight
          totalWeight += kpi.weight
        }
      })

      return totalWeight > 0 ? Math.round((totalWeightedScore / totalWeight) * 10) / 10 : 0
    })

    // 计算风险等级
    const calculateRiskLevel = computed(() => {
      const score = calculateOverallScore.value
      const qualityScore = getCategoryScore('质量指标')
      const deliveryScore = getCategoryScore('交付指标')

      // 基于综合得分和关键指标的风险评估逻辑
      if (score >= 90 && qualityScore >= 90 && deliveryScore >= 90) {
        return 'normal'  // 正常
      } else if (score >= 80 && qualityScore >= 80) {
        return 'low'     // 低风险
      } else if (score >= 70 || (score >= 60 && qualityScore >= 70)) {
        return 'medium'  // 中风险
      } else {
        return 'high'    // 高风险
      }
    })

    // 风险标记相关方法
    const getRiskLevelType = (riskLevel) => {
      const types = {
        normal: 'success',
        low: 'info',
        medium: 'warning',
        high: 'danger'
      }
      return types[riskLevel] || 'info'
    }

    const getRiskLevelClass = (riskLevel) => {
      const classes = {
        normal: 'risk-normal',
        low: 'risk-low',
        medium: 'risk-medium',
        high: 'risk-high'
      }
      return classes[riskLevel] || ''
    }

    const getRiskLevelText = (riskLevel) => {
      const texts = {
        normal: '正常',
        low: '低风险',
        medium: '中风险',
        high: '高风险'
      }
      return texts[riskLevel] || riskLevel
    }

    const getRiskIcon = (riskLevel) => {
      const icons = {
        normal: 'CircleCheck',
        low: 'InfoFilled',
        medium: 'Warning',
        high: 'CircleClose'
      }
      return icons[riskLevel] || 'InfoFilled'
    }

    // 组件挂载时初始化数据
    onMounted(() => {
      initKPIData()
      // 设置默认评估日期为今天
      evaluationInfo.evaluationDate = new Date().toISOString().split('T')[0]
    })

    return {
      supplierInfo,
      evaluationInfo,
      activeKPITab,
      qualityKPIs,
      deliveryKPIs,
      costKPIs,
      serviceKPIs,
      getCategoryWeight,
      getCategoryScore,
      getScoreType,
      getAchievementColor,
      handleActualValueChange,
      handleScoreChange,
      handleAutoCalculate,
      handleImportData,
      handleKPITabChange,
      handleBack,
      handleSaveEvaluation,
      handleSubmitEvaluation,
      calculateOverallScore,
      calculateRiskLevel,
      getRiskLevelType,
      getRiskLevelClass,
      getRiskLevelText,
      getRiskIcon
    }
  }
}
</script>

<style scoped>
.detailed-evaluation-container {
  padding: 0;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button {
  margin-right: 20px;
}

.header-content {
  flex: 1;
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.header-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.evaluation-info-card {
  margin-bottom: 20px;
}

.score-overview-card {
  margin-bottom: 20px;
}

.score-item {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.score-item:hover {
  transform: translateY(-2px);
}

.score-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.score-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.score-weight {
  font-size: 12px;
  color: #606266;
}

.kpi-evaluation-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-tags {
  display: flex;
  gap: 10px;
  align-items: center;
}

.kpi-category-section {
  margin-bottom: 30px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.tab-label .el-icon {
  font-size: 16px;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  padding: 10px 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 6px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.kpi-table {
  margin-bottom: 20px;
}

.achievement-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.achievement-text {
  font-size: 12px;
  font-weight: 500;
}

/* 风险标记样式 */
.risk-icon {
  margin-right: 4px;
}

.risk-normal {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-low {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-medium {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-high {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  border: none;
  color: white;
  font-weight: 500;
  animation: pulse-warning 2s infinite;
}

/* 风险警告动画 */
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}
</style>
