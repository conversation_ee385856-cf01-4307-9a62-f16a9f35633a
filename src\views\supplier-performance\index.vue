<template>
  <div class="supplier-performance-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>供应商绩效管理</h2>
      <p>对供应商绩效进行持续、量化和可视化的监控与评估，驱动供应商持续改进</p>
    </div>

    <!-- 绩效概览卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">85</div>
            <div class="stats-label">平均绩效得分</div>
          </div>
          <el-icon class="stats-icon excellent"><Trophy /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">12</div>
            <div class="stats-label">优秀供应商</div>
          </div>
          <el-icon class="stats-icon good"><Star /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">5</div>
            <div class="stats-label">需改进供应商</div>
          </div>
          <el-icon class="stats-icon warning"><Warning /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">2</div>
            <div class="stats-label">改进计划中</div>
          </div>
          <el-icon class="stats-icon improving"><Tools /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能标签页 -->
    <el-card class="main-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 绩效监控 -->
        <el-tab-pane label="绩效监控" name="monitoring">
          <div class="tab-header">
            <div class="filter-area">
              <el-select
                v-model="searchForm.period"
                placeholder="选择时间周期"
                style="width: 150px; margin-right: 10px"
              >
                <el-option label="本月" value="month" />
                <el-option label="本季度" value="quarter" />
                <el-option label="本年度" value="year" />
              </el-select>
              <el-select
                v-model="searchForm.category"
                placeholder="选择产品线"
                style="width: 150px; margin-right: 10px"
              >
                <el-option label="全部" value="" />
                <el-option label="制动系统" value="brake" />
                <el-option label="发动机" value="engine" />
                <el-option label="电子系统" value="electronic" />
              </el-select>
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入供应商名称"
                style="width: 200px; margin-right: 10px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button type="success" @click="handleExportReport">
                <el-icon><Download /></el-icon>
                导出报告
              </el-button>
            </div>
          </div>

          <el-table :data="performanceData" style="width: 100%" v-loading="loading">
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" fixed="left" />
            <el-table-column prop="category" label="产品线" width="100" />
            <el-table-column prop="qualityScore" label="质量得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.qualityScore)">{{ scope.row.qualityScore }}</span>
                  <el-progress
                    :percentage="scope.row.qualityScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.qualityScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="deliveryScore" label="交付得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.deliveryScore)">{{ scope.row.deliveryScore }}</span>
                  <el-progress
                    :percentage="scope.row.deliveryScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.deliveryScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="costScore" label="成本得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.costScore)">{{ scope.row.costScore }}</span>
                  <el-progress
                    :percentage="scope.row.costScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.costScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="serviceScore" label="服务得分" width="100">
              <template #default="scope">
                <div class="score-cell">
                  <span :class="getScoreClass(scope.row.serviceScore)">{{ scope.row.serviceScore }}</span>
                  <el-progress
                    :percentage="scope.row.serviceScore"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getScoreColor(scope.row.serviceScore)"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalScore" label="综合得分" width="120">
              <template #default="scope">
                <div class="total-score">
                  <span :class="getTotalScoreClass(scope.row.totalScore)">{{ scope.row.totalScore }}</span>
                  <el-tag :type="getPerformanceLevel(scope.row.totalScore).type" size="small">
                    {{ getPerformanceLevel(scope.row.totalScore).text }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="trend" label="趋势" width="80">
              <template #default="scope">
                <el-icon :class="getTrendClass(scope.row.trend)">
                  <component :is="getTrendIcon(scope.row.trend)" />
                </el-icon>
              </template>
            </el-table-column>
            <el-table-column prop="riskLevel" label="风险标记" width="120">
              <template #default="scope">
                <div class="risk-indicator">
                  <el-tag
                    :type="getRiskLevelType(scope.row.riskLevel)"
                    :class="getRiskLevelClass(scope.row.riskLevel)"
                    size="small"
                    effect="dark"
                  >
                    <el-icon class="risk-icon">
                      <component :is="getRiskIcon(scope.row.riskLevel)" />
                    </el-icon>
                    {{ getRiskLevelText(scope.row.riskLevel) }}
                  </el-tag>
                  <el-tooltip
                    v-if="scope.row.riskFactors && scope.row.riskFactors.length > 0"
                    :content="getRiskFactorsText(scope.row.riskFactors)"
                    placement="top"
                  >
                    <el-icon class="risk-info-icon"><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="lastUpdate" label="更新时间" width="120" />
            <el-table-column label="操作" width="320" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="handleViewDashboard(scope.row)">
                    仪表盘
                  </el-button>
                  <el-button type="success" size="small" @click="handleViewDetails(scope.row)">
                    详情
                  </el-button>
                  <el-button type="primary" size="small" @click="handleDetailedEvaluation(scope.row)">
                    绩效评估
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleCreateImprovement(scope.row)"
                    v-if="scope.row.totalScore < 70"
                  >
                    改进计划
                  </el-button>
                  <el-dropdown @command="handleCommand">
                    <el-button type="primary" size="small">
                      更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{action: 'history', row: scope.row}">
                          历史记录
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'compare', row: scope.row}">
                          对比分析
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'alert', row: scope.row}" divided>
                          设置预警
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- KPI配置 -->
        <el-tab-pane label="KPI配置" name="kpi">
          <div class="tab-header">
            <el-button type="primary" @click="handleAddKPI">
              <el-icon><Plus /></el-icon>
              新增KPI
            </el-button>
            <el-button type="success" @click="handleConfigWeight">
              <el-icon><Setting /></el-icon>
              权重配置
            </el-button>
          </div>

          <el-table :data="kpiData" style="width: 100%">
            <el-table-column prop="category" label="指标类别" width="120" />
            <el-table-column prop="name" label="指标名称" min-width="200" />
            <el-table-column prop="unit" label="单位" width="80" />
            <el-table-column prop="weight" label="权重" width="80">
              <template #default="scope">
                {{ scope.row.weight }}%
              </template>
            </el-table-column>
            <el-table-column prop="target" label="目标值" width="100" />
            <el-table-column prop="dataSource" label="数据源" width="120" />
            <el-table-column prop="frequency" label="更新频率" width="100" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
                  {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleEditKPI(scope.row)">
                  编辑
                </el-button>
                <el-button type="warning" size="small" @click="handleToggleKPI(scope.row)">
                  {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                </el-button>
                <el-button type="danger" size="small" @click="handleDeleteKPI(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 改进计划 -->
        <el-tab-pane label="改进计划" name="improvement">
          <div class="tab-header">
            <el-button type="primary" @click="handleNewImprovement">
              <el-icon><Plus /></el-icon>
              新增改进计划
            </el-button>
            <div class="filter-area">
              <el-select
                v-model="improvementSearchForm.status"
                placeholder="计划状态"
                style="width: 150px; margin-left: 10px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="进行中" value="ongoing" />
                <el-option label="已完成" value="completed" />
                <el-option label="已逾期" value="overdue" />
              </el-select>
              <el-button type="primary" @click="handleImprovementSearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="improvementData" style="width: 100%">
            <el-table-column prop="planNo" label="计划编号" width="140" />
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
            <el-table-column prop="problemArea" label="问题领域" width="120" />
            <el-table-column prop="targetScore" label="目标得分" width="100" />
            <el-table-column prop="currentScore" label="当前得分" width="100" />
            <el-table-column prop="responsible" label="负责人" width="100" />
            <el-table-column prop="deadline" label="截止时间" width="120" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getImprovementStatusType(scope.row.status)">
                  {{ getImprovementStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="scope">
                <el-progress :percentage="scope.row.progress" :stroke-width="6" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="scope">
                <el-button type="primary" size="small" @click="handleViewImprovement(scope.row)">
                  查看
                </el-button>
                <el-button type="success" size="small" @click="handleEditImprovement(scope.row)">
                  编辑
                </el-button>
                <el-button type="warning" size="small" @click="handleCloseImprovement(scope.row)">
                  关闭
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>



    <!-- KPI配置弹窗 -->
    <el-dialog
      v-model="kpiDialogVisible"
      :title="kpiDialogTitle"
      width="600px"
      :before-close="handleKpiDialogClose"
    >
      <el-form :model="kpiForm" :rules="kpiRules" ref="kpiFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="指标类别" prop="category">
              <el-select v-model="kpiForm.category" placeholder="请选择指标类别">
                <el-option label="质量指标" value="质量指标" />
                <el-option label="交付指标" value="交付指标" />
                <el-option label="成本指标" value="成本指标" />
                <el-option label="服务指标" value="服务指标" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="指标名称" prop="name">
              <el-input v-model="kpiForm.name" placeholder="请输入指标名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计量单位" prop="unit">
              <el-input v-model="kpiForm.unit" placeholder="如：%、PPM等" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权重" prop="weight">
              <el-input-number
                v-model="kpiForm.weight"
                :min="0"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目标值" prop="target">
              <el-input v-model="kpiForm.target" placeholder="如：>95%、<100PPM" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据来源" prop="dataSource">
              <el-select v-model="kpiForm.dataSource" placeholder="请选择数据来源">
                <el-option label="ERP系统" value="ERP系统" />
                <el-option label="MES系统" value="MES系统" />
                <el-option label="WMS系统" value="WMS系统" />
                <el-option label="手工录入" value="手工录入" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="更新频率" prop="frequency">
          <el-select v-model="kpiForm.frequency" placeholder="请选择更新频率">
            <el-option label="每日" value="每日" />
            <el-option label="每周" value="每周" />
            <el-option label="每月" value="每月" />
            <el-option label="每季度" value="每季度" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="kpiDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleKpiSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 改进计划弹窗 -->
    <el-dialog
      v-model="improvementDialogVisible"
      :title="improvementDialogTitle"
      width="700px"
      :before-close="handleImprovementDialogClose"
    >
      <el-form :model="improvementForm" :rules="improvementRules" ref="improvementFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划编号" prop="planNo">
              <el-input v-model="improvementForm.planNo" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="supplierName">
              <el-select v-model="improvementForm.supplierName" placeholder="请选择供应商" filterable>
                <el-option
                  v-for="supplier in performanceData"
                  :key="supplier.id"
                  :label="supplier.supplierName"
                  :value="supplier.supplierName"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问题领域" prop="problemArea">
              <el-select v-model="improvementForm.problemArea" placeholder="请选择问题领域">
                <el-option label="质量问题" value="质量问题" />
                <el-option label="交付问题" value="交付问题" />
                <el-option label="成本问题" value="成本问题" />
                <el-option label="服务问题" value="服务问题" />
                <el-option label="综合绩效" value="综合绩效" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标得分" prop="targetScore">
              <el-input-number
                v-model="improvementForm.targetScore"
                :min="0"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="responsible">
              <el-input v-model="improvementForm.responsible" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker
                v-model="improvementForm.deadline"
                type="date"
                placeholder="请选择截止时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="问题描述" prop="problemDescription">
          <el-input
            v-model="improvementForm.problemDescription"
            type="textarea"
            :rows="3"
            placeholder="请详细描述存在的问题"
          />
        </el-form-item>

        <el-form-item label="改进措施" prop="improvementMeasures">
          <el-input
            v-model="improvementForm.improvementMeasures"
            type="textarea"
            :rows="3"
            placeholder="请描述具体的改进措施"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="improvementDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleImprovementSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'SupplierPerformance',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const activeTab = ref('monitoring')

    // 搜索表单
    const searchForm = reactive({
      period: 'quarter',
      category: '',
      keyword: ''
    })

    const improvementSearchForm = reactive({
      status: ''
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 绩效数据
    const performanceData = ref([])
    
    // KPI数据
    const kpiData = ref([])
    
    // 改进计划数据
    const improvementData = ref([])

    // 弹窗控制
    const kpiDialogVisible = ref(false)
    const improvementDialogVisible = ref(false)

    // 弹窗标题
    const kpiDialogTitle = ref('新增KPI')
    const improvementDialogTitle = ref('新增改进计划')

    // 表单引用
    const kpiFormRef = ref(null)
    const improvementFormRef = ref(null)



    // KPI配置表单
    const kpiForm = reactive({
      id: '',
      category: '',
      name: '',
      unit: '',
      weight: 0,
      target: '',
      dataSource: '',
      frequency: '',
      status: 'active'
    })

    // 改进计划表单
    const improvementForm = reactive({
      id: '',
      planNo: '',
      supplierName: '',
      problemArea: '',
      targetScore: 0,
      responsible: '',
      deadline: '',
      problemDescription: '',
      improvementMeasures: '',
      status: 'ongoing',
      progress: 0
    })

    // 表单验证规则

    const kpiRules = {
      category: [{ required: true, message: '请选择指标类别', trigger: 'change' }],
      name: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
      unit: [{ required: true, message: '请输入计量单位', trigger: 'blur' }],
      weight: [{ required: true, message: '请输入权重', trigger: 'blur' }],
      target: [{ required: true, message: '请输入目标值', trigger: 'blur' }],
      dataSource: [{ required: true, message: '请选择数据来源', trigger: 'change' }],
      frequency: [{ required: true, message: '请选择更新频率', trigger: 'change' }]
    }

    const improvementRules = {
      supplierName: [{ required: true, message: '请选择供应商', trigger: 'change' }],
      problemArea: [{ required: true, message: '请选择问题领域', trigger: 'change' }],
      targetScore: [{ required: true, message: '请输入目标得分', trigger: 'blur' }],
      responsible: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
      deadline: [{ required: true, message: '请选择截止时间', trigger: 'change' }],
      problemDescription: [{ required: true, message: '请输入问题描述', trigger: 'blur' }],
      improvementMeasures: [{ required: true, message: '请输入改进措施', trigger: 'blur' }]
    }

    // 初始化数据
    const initPerformanceData = () => {
      performanceData.value = [
        {
          id: 1,
          supplierName: '博世汽车部件(苏州)有限公司',
          category: '制动系统',
          qualityScore: 92,
          deliveryScore: 88,
          costScore: 85,
          serviceScore: 90,
          totalScore: 89,
          trend: 'up',
          lastUpdate: '2024-03-01',
          riskLevel: 'low',
          riskFactors: ['总分89分(良好)', '质量得分92分', '趋势向上']
        },
        {
          id: 2,
          supplierName: '大陆汽车系统(常熟)有限公司',
          category: '电子系统',
          qualityScore: 85,
          deliveryScore: 92,
          costScore: 78,
          serviceScore: 88,
          totalScore: 86,
          trend: 'stable',
          lastUpdate: '2024-03-01',
          riskLevel: 'low',
          riskFactors: ['总分86分(良好)', '交付得分优秀', '成本需关注']
        },
        {
          id: 3,
          supplierName: '法雷奥汽车空调湖北有限公司',
          category: '发动机',
          qualityScore: 75,
          deliveryScore: 70,
          costScore: 82,
          serviceScore: 76,
          totalScore: 75,
          trend: 'down',
          lastUpdate: '2024-03-01',
          riskLevel: 'medium',
          riskFactors: ['总分75分(一般)', '下降趋势', '交付得分偏低']
        },
        {
          id: 4,
          supplierName: '麦格纳汽车技术(上海)有限公司',
          category: '制动系统',
          qualityScore: 95,
          deliveryScore: 94,
          costScore: 88,
          serviceScore: 92,
          totalScore: 92,
          trend: 'up',
          lastUpdate: '2024-03-01',
          riskLevel: 'normal',
          riskFactors: ['总分92分(优秀)', '质量得分95分', '各项指标均衡']
        },
        {
          id: 5,
          supplierName: '安波福电气系统有限公司',
          category: '电子系统',
          qualityScore: 65,
          deliveryScore: 68,
          costScore: 70,
          serviceScore: 65,
          totalScore: 67,
          trend: 'down',
          lastUpdate: '2024-03-01',
          riskLevel: 'high',
          riskFactors: ['总分67分(不达标)', '下降趋势', '各项指标偏低']
        }
      ]
      pagination.total = performanceData.value.length
    }

    const initKPIData = () => {
      kpiData.value = [
        {
          id: 1,
          category: '质量指标',
          name: '来料不良率(PPM)',
          unit: 'PPM',
          weight: 25,
          target: '<100',
          dataSource: 'MES系统',
          frequency: '每日',
          status: 'active'
        },
        {
          id: 2,
          category: '质量指标',
          name: '批次合格率',
          unit: '%',
          weight: 25,
          target: '>99%',
          dataSource: 'MES系统',
          frequency: '每日',
          status: 'active'
        },
        {
          id: 3,
          category: '交付指标',
          name: '交期达成率(OTD)',
          unit: '%',
          weight: 20,
          target: '>95%',
          dataSource: 'ERP系统',
          frequency: '每日',
          status: 'active'
        },
        {
          id: 4,
          category: '交付指标',
          name: '订单完成率',
          unit: '%',
          weight: 10,
          target: '>98%',
          dataSource: 'ERP系统',
          frequency: '每周',
          status: 'active'
        },
        {
          id: 5,
          category: '成本指标',
          name: '年度降价达成率',
          unit: '%',
          weight: 15,
          target: '>3%',
          dataSource: '手工录入',
          frequency: '每季度',
          status: 'active'
        },
        {
          id: 6,
          category: '服务指标',
          name: '技术支持及时性',
          unit: '小时',
          weight: 5,
          target: '<24',
          dataSource: '手工录入',
          frequency: '每月',
          status: 'active'
        }
      ]
    }

    const initImprovementData = () => {
      improvementData.value = [
        {
          id: 1,
          planNo: 'IMP2024001',
          supplierName: '法雷奥汽车空调湖北有限公司',
          problemArea: '交付质量',
          targetScore: 85,
          currentScore: 75,
          responsible: '采购部-李经理',
          deadline: '2024-06-30',
          status: 'ongoing',
          progress: 60
        },
        {
          id: 2,
          planNo: 'IMP2024002',
          supplierName: '安波福电气系统有限公司',
          problemArea: '综合绩效',
          targetScore: 80,
          currentScore: 67,
          responsible: '质量部-张工程师',
          deadline: '2024-05-31',
          status: 'ongoing',
          progress: 40
        },
        {
          id: 3,
          planNo: 'IMP2023015',
          supplierName: '某改进完成供应商',
          problemArea: '成本控制',
          targetScore: 85,
          currentScore: 88,
          responsible: '采购部-王主管',
          deadline: '2024-02-28',
          status: 'completed',
          progress: 100
        }
      ]
    }

    // 评分相关方法
    const getScoreClass = (score) => {
      if (score >= 90) return 'score-excellent'
      if (score >= 80) return 'score-good'
      if (score >= 70) return 'score-average'
      return 'score-poor'
    }

    const getScoreColor = (score) => {
      if (score >= 90) return '#67C23A'
      if (score >= 80) return '#409EFF'
      if (score >= 70) return '#E6A23C'
      return '#F56C6C'
    }

    const getTotalScoreClass = (score) => {
      if (score >= 90) return 'total-score-excellent'
      if (score >= 80) return 'total-score-good'
      if (score >= 70) return 'total-score-average'
      return 'total-score-poor'
    }

    const getPerformanceLevel = (score) => {
      if (score >= 90) return { type: 'success', text: 'A级' }
      if (score >= 80) return { type: 'primary', text: 'B级' }
      if (score >= 70) return { type: 'warning', text: 'C级' }
      return { type: 'danger', text: 'D级' }
    }

    const getTrendClass = (trend) => {
      const classes = {
        up: 'trend-up',
        down: 'trend-down',
        stable: 'trend-stable'
      }
      return classes[trend] || ''
    }

    const getTrendIcon = (trend) => {
      const icons = {
        up: 'TrendCharts',
        down: 'Bottom',
        stable: 'Minus'
      }
      return icons[trend] || 'Minus'
    }

    // 风险标记相关方法 - 基于绩效评估数据
    const getRiskLevelType = (riskLevel) => {
      const types = {
        normal: 'success',
        low: 'info',
        medium: 'warning',
        high: 'danger'
      }
      return types[riskLevel] || 'info'
    }

    const getRiskLevelClass = (riskLevel) => {
      const classes = {
        normal: 'risk-normal',
        low: 'risk-low',
        medium: 'risk-medium',
        high: 'risk-high'
      }
      return classes[riskLevel] || ''
    }

    const getRiskLevelText = (riskLevel) => {
      const texts = {
        normal: '正常',
        low: '低风险',
        medium: '中风险',
        high: '高风险'
      }
      return texts[riskLevel] || riskLevel
    }

    const getRiskIcon = (riskLevel) => {
      const icons = {
        normal: 'CircleCheck',
        low: 'InfoFilled',
        medium: 'Warning',
        high: 'CircleClose'
      }
      return icons[riskLevel] || 'InfoFilled'
    }

    const getRiskFactorsText = (riskFactors) => {
      if (!riskFactors || riskFactors.length === 0) return ''
      return `风险因素：${riskFactors.join('、')}`
    }

    // 根据绩效评估自动计算风险等级
    const calculateRiskFromPerformance = (totalScore, trend, qualityScore, deliveryScore) => {
      // 基于绩效评估的风险计算逻辑
      if (totalScore >= 90 && qualityScore >= 90 && deliveryScore >= 90) {
        return 'normal'
      } else if (totalScore >= 80 && trend !== 'down') {
        return 'low'
      } else if (totalScore >= 70 || (totalScore >= 60 && trend === 'up')) {
        return 'medium'
      } else {
        return 'high'
      }
    }

    const getImprovementStatusType = (status) => {
      const types = {
        ongoing: 'warning',
        completed: 'success',
        overdue: 'danger',
        cancelled: 'info'
      }
      return types[status] || ''
    }

    const getImprovementStatusText = (status) => {
      const texts = {
        ongoing: '进行中',
        completed: '已完成',
        overdue: '已逾期',
        cancelled: '已取消'
      }
      return texts[status] || status
    }

    // 事件处理方法
    const handleTabChange = (tabName) => {
      activeTab.value = tabName
    }

    const handleSearch = () => {
      console.log('搜索绩效数据', searchForm)
    }

    const handleExportReport = () => {
      console.log('导出绩效报告')
    }

    const handleViewDashboard = (row) => {
      router.push(`/supplier-performance/dashboard/${row.id}`)
    }

    const handleViewDetails = (row) => {
      console.log('查看详情', row)
    }

    const handleCreateImprovement = (row) => {
      router.push(`/supplier-performance/improvement/${row.id}`)
    }

    const handleDetailedEvaluation = (row) => {
      router.push(`/supplier-performance/detailed-evaluation/${row.id}`)
    }

    const handleCommand = (command) => {
      console.log('执行操作', command)
    }

    const handleAddKPI = () => {
      kpiDialogTitle.value = '新增KPI'
      resetKpiForm()
      kpiDialogVisible.value = true
    }

    const handleConfigWeight = () => {
      console.log('权重配置')
    }

    const handleEditKPI = (row) => {
      kpiDialogTitle.value = '编辑KPI'
      Object.assign(kpiForm, row)
      kpiDialogVisible.value = true
    }

    const handleToggleKPI = (row) => {
      row.status = row.status === 'active' ? 'inactive' : 'active'
    }

    const handleDeleteKPI = (row) => {
      console.log('删除KPI', row)
    }

    const handleNewImprovement = () => {
      improvementDialogTitle.value = '新增改进计划'
      resetImprovementForm()
      generatePlanNo()
      improvementDialogVisible.value = true
    }

    const handleImprovementSearch = () => {
      console.log('搜索改进计划', improvementSearchForm)
    }

    const handleViewImprovement = (row) => {
      router.push(`/supplier-performance/improvement/${row.id}`)
    }

    const handleEditImprovement = (row) => {
      improvementDialogTitle.value = '编辑改进计划'
      Object.assign(improvementForm, row)
      improvementDialogVisible.value = true
    }

    const handleCloseImprovement = (row) => {
      console.log('关闭改进计划', row)
    }

    const handleSizeChange = (val) => {
      pagination.pageSize = val
    }

    const handleCurrentChange = (val) => {
      pagination.currentPage = val
    }



    // KPI配置相关方法
    const handleKpiDialogClose = () => {
      kpiDialogVisible.value = false
      resetKpiForm()
    }

    const handleKpiSubmit = async () => {
      if (!kpiFormRef.value) return

      try {
        await kpiFormRef.value.validate()
        console.log('提交KPI配置', kpiForm)

        if (kpiForm.id) {
          // 编辑模式
          const index = kpiData.value.findIndex(item => item.id === kpiForm.id)
          if (index !== -1) {
            Object.assign(kpiData.value[index], kpiForm)
          }
        } else {
          // 新增模式
          const newKpi = { ...kpiForm, id: Date.now() }
          kpiData.value.push(newKpi)
        }

        kpiDialogVisible.value = false
        resetKpiForm()
      } catch (error) {
        console.log('表单验证失败', error)
      }
    }

    const resetKpiForm = () => {
      Object.assign(kpiForm, {
        id: '',
        category: '',
        name: '',
        unit: '',
        weight: 0,
        target: '',
        dataSource: '',
        frequency: '',
        status: 'active'
      })
      if (kpiFormRef.value) {
        kpiFormRef.value.clearValidate()
      }
    }

    // 改进计划相关方法
    const handleImprovementDialogClose = () => {
      improvementDialogVisible.value = false
      resetImprovementForm()
    }

    const handleImprovementSubmit = async () => {
      if (!improvementFormRef.value) return

      try {
        await improvementFormRef.value.validate()
        console.log('提交改进计划', improvementForm)

        if (improvementForm.id) {
          // 编辑模式
          const index = improvementData.value.findIndex(item => item.id === improvementForm.id)
          if (index !== -1) {
            Object.assign(improvementData.value[index], improvementForm)
          }
        } else {
          // 新增模式
          const newPlan = { ...improvementForm, id: Date.now(), currentScore: 0 }
          improvementData.value.push(newPlan)
        }

        improvementDialogVisible.value = false
        resetImprovementForm()
      } catch (error) {
        console.log('表单验证失败', error)
      }
    }

    const resetImprovementForm = () => {
      Object.assign(improvementForm, {
        id: '',
        planNo: '',
        supplierName: '',
        problemArea: '',
        targetScore: 0,
        responsible: '',
        deadline: '',
        problemDescription: '',
        improvementMeasures: '',
        status: 'ongoing',
        progress: 0
      })
      if (improvementFormRef.value) {
        improvementFormRef.value.clearValidate()
      }
    }

    const generatePlanNo = () => {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const random = String(Math.floor(Math.random() * 1000)).padStart(3, '0')
      improvementForm.planNo = `IMP${year}${month}${day}${random}`
    }

    onMounted(() => {
      initPerformanceData()
      initKPIData()
      initImprovementData()
    })

    return {
      loading,
      activeTab,
      searchForm,
      improvementSearchForm,
      pagination,
      performanceData,
      kpiData,
      improvementData,
      // 弹窗控制
      kpiDialogVisible,
      improvementDialogVisible,
      kpiDialogTitle,
      improvementDialogTitle,
      // 表单数据
      kpiForm,
      improvementForm,
      // 表单验证规则
      kpiRules,
      improvementRules,
      // 表单引用
      kpiFormRef,
      improvementFormRef,
      // 工具方法
      getScoreClass,
      getScoreColor,
      getTotalScoreClass,
      getPerformanceLevel,
      getTrendClass,
      getTrendIcon,
      getImprovementStatusType,
      getImprovementStatusText,
      getRiskLevelType,
      getRiskLevelClass,
      getRiskLevelText,
      getRiskIcon,
      getRiskFactorsText,
      calculateRiskFromPerformance,
      // 事件处理方法
      handleTabChange,
      handleSearch,
      handleExportReport,
      handleViewDashboard,
      handleViewDetails,
      handleDetailedEvaluation,
      handleCreateImprovement,
      handleCommand,
      handleAddKPI,
      handleConfigWeight,
      handleEditKPI,
      handleToggleKPI,
      handleDeleteKPI,
      handleNewImprovement,
      handleImprovementSearch,
      handleViewImprovement,
      handleEditImprovement,
      handleCloseImprovement,
      handleSizeChange,
      handleCurrentChange,
      // 弹窗相关方法
      handleKpiDialogClose,
      handleKpiSubmit,
      handleImprovementDialogClose,
      handleImprovementSubmit
    }
  }
}
</script>

<style scoped>
.supplier-performance-container {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-content {
  padding: 10px 0;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.stats-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.stats-icon.excellent {
  color: #67C23A;
}

.stats-icon.good {
  color: #409EFF;
}

.stats-icon.warning {
  color: #E6A23C;
}

.stats-icon.improving {
  color: #909399;
}

.main-card {
  margin-bottom: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-area {
  display: flex;
  align-items: center;
}

.score-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.score-cell span {
  font-weight: bold;
  font-size: 12px;
}

.score-excellent {
  color: #67C23A;
}

.score-good {
  color: #409EFF;
}

.score-average {
  color: #E6A23C;
}

.score-poor {
  color: #F56C6C;
}

.total-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.total-score span {
  font-weight: bold;
  font-size: 16px;
}

.total-score-excellent {
  color: #67C23A;
}

.total-score-good {
  color: #409EFF;
}

.total-score-average {
  color: #E6A23C;
}

.total-score-poor {
  color: #F56C6C;
}

.trend-up {
  color: #67C23A;
  font-size: 18px;
}

.trend-down {
  color: #F56C6C;
  font-size: 18px;
}

.trend-stable {
  color: #909399;
  font-size: 18px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: auto;
  padding: 5px 8px;
  font-size: 12px;
}

.action-buttons .el-dropdown {
  margin-left: 4px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons .el-button {
    padding: 4px 6px;
    font-size: 11px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .filter-area {
    flex-direction: column;
    gap: 10px;
  }

  .filter-area .el-input,
  .filter-area .el-select {
    width: 100% !important;
  }
}

/* 风险标记样式 */
.risk-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.risk-icon {
  margin-right: 2px;
}

.risk-info-icon {
  color: #909399;
  cursor: pointer;
  font-size: 14px;
}

.risk-info-icon:hover {
  color: #409EFF;
}

/* 风险等级特殊样式 - 基于绩效评估 */
.risk-normal {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-low {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-medium {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-high {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  border: none;
  color: white;
  font-weight: 500;
  animation: pulse-warning 2s infinite;
}

/* 风险警告动画 */
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(230, 162, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(230, 162, 60, 0);
  }
}


</style>
