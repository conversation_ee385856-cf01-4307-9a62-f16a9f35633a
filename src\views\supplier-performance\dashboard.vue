<template>
  <div class="performance-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>{{ supplierInfo.name }} - 绩效仪表盘</h2>
      <div class="header-actions">
        <el-button type="success" @click="handleExportDashboard">
          <el-icon><Download /></el-icon>
          导出仪表盘
        </el-button>
        <el-button type="primary" @click="handleRefreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 供应商基本信息 -->
    <el-card class="info-card">
      <template #header>
        <span>供应商基本信息</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="info-item">
            <label>供应商名称：</label>
            <span>{{ supplierInfo.name }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <label>产品线：</label>
            <span>{{ supplierInfo.category }}</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <label>合作年限：</label>
            <span>{{ supplierInfo.cooperationYears }}年</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="info-item">
            <label>零件重要度：</label>
            <el-tag :type="getPartLevelType(supplierInfo.partLevel)">
              {{ supplierInfo.partLevel }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 综合绩效概览 -->
    <el-row :gutter="20" class="overview-row">
      <el-col :span="6">
        <el-card class="performance-card">
          <div class="performance-content">
            <div class="performance-score">{{ overallPerformance.totalScore }}</div>
            <div class="performance-label">综合得分</div>
            <div class="performance-tags">
              <el-tag :type="getPerformanceLevel(overallPerformance.totalScore).type">
                {{ getPerformanceLevel(overallPerformance.totalScore).text }}
              </el-tag>
              <el-tag
                :type="getRiskLevelType(calculateRiskLevel)"
                :class="getRiskLevelClass(calculateRiskLevel)"
                effect="dark"
                size="small"
              >
                <el-icon class="risk-icon">
                  <component :is="getRiskIcon(calculateRiskLevel)" />
                </el-icon>
                {{ getRiskLevelText(calculateRiskLevel) }}
              </el-tag>
            </div>
          </div>
          <div class="performance-trend">
            <el-icon :class="getTrendClass(overallPerformance.trend)">
              <component :is="getTrendIcon(overallPerformance.trend)" />
            </el-icon>
            <span>{{ getTrendText(overallPerformance.trend) }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="performance-card quality">
          <div class="performance-content">
            <div class="performance-score">{{ overallPerformance.qualityScore }}</div>
            <div class="performance-label">质量得分</div>
            <el-progress :percentage="overallPerformance.qualityScore" :stroke-width="6" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="performance-card delivery">
          <div class="performance-content">
            <div class="performance-score">{{ overallPerformance.deliveryScore }}</div>
            <div class="performance-label">交付得分</div>
            <el-progress :percentage="overallPerformance.deliveryScore" :stroke-width="6" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="performance-card cost">
          <div class="performance-content">
            <div class="performance-score">{{ overallPerformance.costScore }}</div>
            <div class="performance-label">成本得分</div>
            <el-progress :percentage="overallPerformance.costScore" :stroke-width="6" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细KPI指标 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>详细KPI指标</span>
          <el-select v-model="selectedPeriod" @change="handlePeriodChange" style="width: 150px">
            <el-option label="本月" value="month" />
            <el-option label="本季度" value="quarter" />
            <el-option label="本年度" value="year" />
          </el-select>
        </div>
      </template>
      <el-table :data="kpiDetails" style="width: 100%">
        <el-table-column prop="category" label="指标类别" width="120" />
        <el-table-column prop="name" label="指标名称" min-width="200" />
        <el-table-column prop="currentValue" label="当前值" width="100" />
        <el-table-column prop="targetValue" label="目标值" width="100" />
        <el-table-column prop="achievement" label="达成率" width="120">
          <template #default="scope">
            <el-progress
              :percentage="scope.row.achievement"
              :stroke-width="6"
              :color="getAchievementColor(scope.row.achievement)"
            />
            <span style="margin-left: 8px; font-size: 12px;">{{ scope.row.achievement }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="得分" width="80">
          <template #default="scope">
            <span :class="getScoreClass(scope.row.score)">{{ scope.row.score }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="trend" label="趋势" width="80">
          <template #default="scope">
            <el-icon :class="getTrendClass(scope.row.trend)">
              <component :is="getTrendIcon(scope.row.trend)" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="lastUpdate" label="更新时间" width="120" />
      </el-table>
    </el-card>

    <!-- 绩效趋势图 -->
    <el-card class="info-card">
      <template #header>
        <span>绩效趋势分析</span>
      </template>
      <div class="chart-container">
        <div class="chart-placeholder">
          <el-icon style="font-size: 48px; color: #ddd;"><TrendCharts /></el-icon>
          <p style="color: #999; margin-top: 10px;">绩效趋势图表区域</p>
          <p style="color: #999; font-size: 12px;">（实际项目中可集成 ECharts 或其他图表库）</p>
        </div>
      </div>
    </el-card>

    <!-- 预警信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>预警信息</span>
          <el-tag :type="getAlertLevelType(alertSummary.level)">
            {{ alertSummary.count }}个预警
          </el-tag>
        </div>
      </template>
      <el-table :data="alertData" style="width: 100%">
        <el-table-column prop="type" label="预警类型" width="120">
          <template #default="scope">
            <el-tag :type="getAlertTypeColor(scope.row.type)" size="small">
              {{ scope.row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="indicator" label="指标名称" min-width="200" />
        <el-table-column prop="currentValue" label="当前值" width="100" />
        <el-table-column prop="threshold" label="阈值" width="100" />
        <el-table-column prop="severity" label="严重程度" width="100">
          <template #default="scope">
            <el-tag :type="getSeverityType(scope.row.severity)" size="small">
              {{ scope.row.severity }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="持续时间" width="100" />
        <el-table-column prop="triggerTime" label="触发时间" width="120" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleProcessAlert(scope.row)">
              处理
            </el-button>
            <el-button type="info" size="small" @click="handleIgnoreAlert(scope.row)">
              忽略
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 改进建议 -->
    <el-card class="info-card">
      <template #header>
        <span>改进建议</span>
      </template>
      <div class="suggestions-container">
        <div v-for="suggestion in improvementSuggestions" :key="suggestion.id" class="suggestion-item">
          <div class="suggestion-header">
            <el-tag :type="getSuggestionPriorityType(suggestion.priority)" size="small">
              {{ suggestion.priority }}优先级
            </el-tag>
            <span class="suggestion-area">{{ suggestion.area }}</span>
          </div>
          <div class="suggestion-content">
            <h4>{{ suggestion.title }}</h4>
            <p>{{ suggestion.description }}</p>
            <div class="suggestion-actions">
              <el-button type="primary" size="small" @click="handleCreateImprovementPlan(suggestion)">
                创建改进计划
              </el-button>
              <el-button type="info" size="small" @click="handleViewSuggestionDetail(suggestion)">
                查看详情
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export default {
  name: 'PerformanceDashboard',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const selectedPeriod = ref('quarter')

    const supplierInfo = reactive({
      name: '博世汽车部件(苏州)有限公司',
      category: '制动系统',
      cooperationYears: 8,
      partLevel: 'A级关键件'
    })

    const overallPerformance = reactive({
      totalScore: 89,
      qualityScore: 92,
      deliveryScore: 88,
      costScore: 85,
      serviceScore: 90,
      trend: 'up'
    })

    const alertSummary = reactive({
      count: 2,
      level: 'warning'
    })

    const kpiDetails = ref([
      {
        category: '质量指标',
        name: '来料不良率(PPM)',
        currentValue: '85 PPM',
        targetValue: '<100 PPM',
        achievement: 115,
        score: 95,
        trend: 'up',
        lastUpdate: '2024-03-01'
      },
      {
        category: '质量指标',
        name: '批次合格率',
        currentValue: '99.2%',
        targetValue: '>99%',
        achievement: 100,
        score: 92,
        trend: 'stable',
        lastUpdate: '2024-03-01'
      },
      {
        category: '交付指标',
        name: '交期达成率(OTD)',
        currentValue: '94%',
        targetValue: '>95%',
        achievement: 99,
        score: 88,
        trend: 'down',
        lastUpdate: '2024-03-01'
      },
      {
        category: '交付指标',
        name: '订单完成率',
        currentValue: '98.5%',
        targetValue: '>98%',
        achievement: 100,
        score: 90,
        trend: 'up',
        lastUpdate: '2024-03-01'
      },
      {
        category: '成本指标',
        name: '年度降价达成率',
        currentValue: '3.2%',
        targetValue: '>3%',
        achievement: 107,
        score: 85,
        trend: 'stable',
        lastUpdate: '2024-03-01'
      }
    ])

    const alertData = ref([
      {
        type: '质量预警',
        indicator: '交期达成率(OTD)',
        currentValue: '94%',
        threshold: '95%',
        severity: '中等',
        duration: '3天',
        triggerTime: '2024-02-28'
      },
      {
        type: '趋势预警',
        indicator: '客户投诉率',
        currentValue: '0.8%',
        threshold: '0.5%',
        severity: '轻微',
        duration: '1天',
        triggerTime: '2024-03-01'
      }
    ])

    const improvementSuggestions = ref([
      {
        id: 1,
        priority: '高',
        area: '交付管理',
        title: '优化交付计划管理',
        description: '建议加强与供应商的交付计划协调，建立更精确的交付预测模型，提高交期达成率。'
      },
      {
        id: 2,
        priority: '中',
        area: '质量控制',
        title: '强化过程质量控制',
        description: '建议在关键工序增加质量控制点，实施更严格的过程监控，降低客户投诉率。'
      },
      {
        id: 3,
        priority: '低',
        area: '成本优化',
        title: '探索成本降低机会',
        description: '建议与供应商共同分析成本结构，寻找进一步的成本优化空间。'
      }
    ])

    // 状态相关方法
    const getPartLevelType = (level) => {
      const types = {
        'A级关键件': 'danger',
        'B级重要件': 'warning',
        'C级普通件': 'info'
      }
      return types[level] || ''
    }

    const getPerformanceLevel = (score) => {
      if (score >= 90) return { type: 'success', text: 'A级' }
      if (score >= 80) return { type: 'primary', text: 'B级' }
      if (score >= 70) return { type: 'warning', text: 'C级' }
      return { type: 'danger', text: 'D级' }
    }

    const getTrendClass = (trend) => {
      const classes = {
        up: 'trend-up',
        down: 'trend-down',
        stable: 'trend-stable'
      }
      return classes[trend] || ''
    }

    const getTrendIcon = (trend) => {
      const icons = {
        up: 'TrendCharts',
        down: 'Bottom',
        stable: 'Minus'
      }
      return icons[trend] || 'Minus'
    }

    const getTrendText = (trend) => {
      const texts = {
        up: '上升趋势',
        down: '下降趋势',
        stable: '稳定'
      }
      return texts[trend] || ''
    }

    const getAchievementColor = (achievement) => {
      if (achievement >= 100) return '#67C23A'
      if (achievement >= 90) return '#409EFF'
      if (achievement >= 80) return '#E6A23C'
      return '#F56C6C'
    }

    const getScoreClass = (score) => {
      if (score >= 90) return 'score-excellent'
      if (score >= 80) return 'score-good'
      if (score >= 70) return 'score-average'
      return 'score-poor'
    }

    const getAlertLevelType = (level) => {
      const types = {
        high: 'danger',
        warning: 'warning',
        low: 'info'
      }
      return types[level] || ''
    }

    const getAlertTypeColor = (type) => {
      const colors = {
        '质量预警': 'danger',
        '交付预警': 'warning',
        '成本预警': 'primary',
        '趋势预警': 'info'
      }
      return colors[type] || ''
    }

    const getSeverityType = (severity) => {
      const types = {
        '严重': 'danger',
        '中等': 'warning',
        '轻微': 'info'
      }
      return types[severity] || ''
    }

    const getSuggestionPriorityType = (priority) => {
      const types = {
        '高': 'danger',
        '中': 'warning',
        '低': 'info'
      }
      return types[priority] || ''
    }

    // 事件处理方法
    const handleExportDashboard = () => {
      console.log('导出仪表盘')
    }

    const handleRefreshData = () => {
      console.log('刷新数据')
    }

    const handlePeriodChange = (period) => {
      console.log('切换时间周期', period)
    }

    const handleProcessAlert = (row) => {
      console.log('处理预警', row)
    }

    const handleIgnoreAlert = (row) => {
      console.log('忽略预警', row)
    }

    const handleCreateImprovementPlan = (suggestion) => {
      console.log('创建改进计划', suggestion)
    }

    const handleViewSuggestionDetail = (suggestion) => {
      console.log('查看建议详情', suggestion)
    }

    // 计算风险等级
    const calculateRiskLevel = computed(() => {
      const score = overallPerformance.totalScore
      const qualityScore = overallPerformance.qualityScore
      const deliveryScore = overallPerformance.deliveryScore

      // 基于综合得分和关键指标的风险评估逻辑
      if (score >= 90 && qualityScore >= 90 && deliveryScore >= 90) {
        return 'normal'  // 正常
      } else if (score >= 80 && qualityScore >= 80) {
        return 'low'     // 低风险
      } else if (score >= 70 || (score >= 60 && qualityScore >= 70)) {
        return 'medium'  // 中风险
      } else {
        return 'high'    // 高风险
      }
    })

    // 风险标记相关方法
    const getRiskLevelType = (riskLevel) => {
      const types = {
        normal: 'success',
        low: 'info',
        medium: 'warning',
        high: 'danger'
      }
      return types[riskLevel] || 'info'
    }

    const getRiskLevelClass = (riskLevel) => {
      const classes = {
        normal: 'risk-normal',
        low: 'risk-low',
        medium: 'risk-medium',
        high: 'risk-high'
      }
      return classes[riskLevel] || ''
    }

    const getRiskLevelText = (riskLevel) => {
      const texts = {
        normal: '正常',
        low: '低风险',
        medium: '中风险',
        high: '高风险'
      }
      return texts[riskLevel] || riskLevel
    }

    const getRiskIcon = (riskLevel) => {
      const icons = {
        normal: 'CircleCheck',
        low: 'InfoFilled',
        medium: 'Warning',
        high: 'CircleClose'
      }
      return icons[riskLevel] || 'InfoFilled'
    }

    return {
      selectedPeriod,
      supplierInfo,
      overallPerformance,
      alertSummary,
      kpiDetails,
      alertData,
      improvementSuggestions,
      getPartLevelType,
      getPerformanceLevel,
      getTrendClass,
      getTrendIcon,
      getTrendText,
      getAchievementColor,
      getScoreClass,
      getAlertLevelType,
      getAlertTypeColor,
      getSeverityType,
      getSuggestionPriorityType,
      handleExportDashboard,
      handleRefreshData,
      handlePeriodChange,
      handleProcessAlert,
      handleIgnoreAlert,
      handleCreateImprovementPlan,
      handleViewSuggestionDetail,
      calculateRiskLevel,
      getRiskLevelType,
      getRiskLevelClass,
      getRiskLevelText,
      getRiskIcon
    }
  }
}
</script>

<style scoped>
.performance-dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-header h2 {
  color: #303133;
  margin: 0;
  flex: 1;
  margin-left: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.overview-row {
  margin-bottom: 20px;
}

.performance-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.performance-card.quality {
  border-left: 4px solid #67C23A;
}

.performance-card.delivery {
  border-left: 4px solid #409EFF;
}

.performance-card.cost {
  border-left: 4px solid #E6A23C;
}

.performance-content {
  padding: 10px 0;
}

.performance-score {
  font-size: 36px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.performance-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.performance-tags {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.performance-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-top: 10px;
  font-size: 12px;
  color: #606266;
}

.trend-up {
  color: #67C23A;
  font-size: 16px;
}

.trend-down {
  color: #F56C6C;
  font-size: 16px;
}

.trend-stable {
  color: #909399;
  font-size: 16px;
}

.score-excellent {
  color: #67C23A;
  font-weight: bold;
}

.score-good {
  color: #409EFF;
  font-weight: bold;
}

.score-average {
  color: #E6A23C;
  font-weight: bold;
}

.score-poor {
  color: #F56C6C;
  font-weight: bold;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
}

.suggestions-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.suggestion-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.suggestion-area {
  font-weight: bold;
  color: #303133;
}

.suggestion-content h4 {
  color: #303133;
  margin-bottom: 8px;
  font-size: 16px;
}

.suggestion-content p {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 15px;
}

.suggestion-actions {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .overview-row .el-col {
    margin-bottom: 15px;
  }

  .performance-score {
    font-size: 28px;
  }

  .suggestion-actions {
    flex-direction: column;
  }

  .suggestion-actions .el-button {
    width: 100%;
  }
}

/* 风险标记样式 */
.risk-icon {
  margin-right: 4px;
}

.risk-normal {
  background: linear-gradient(135deg, #67C23A, #85CE61);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-low {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-medium {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
  border: none;
  color: white;
  font-weight: 500;
}

.risk-high {
  background: linear-gradient(135deg, #F56C6C, #F78989);
  border: none;
  color: white;
  font-weight: 500;
  animation: pulse-warning 2s infinite;
}

/* 风险警告动画 */
@keyframes pulse-warning {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}
</style>
